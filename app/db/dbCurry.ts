"use server";
import { ObjectId } from "mongodb";
import { mongoClient } from "@/lib/mongo";
import { transformResults2 } from "@/app/(main site)/Components/db_services/mongo";
import { debug } from "@/app/utils/debug";
import { hash, compare } from "bcrypt";
import { O } from "@measured/puck/dist/resolve-all-data-BoWgijLi";
import { User, FAAQuestion, OPQuestion } from "@/app/db/types";

const SALT_ROUNDS = 10;

/**
 * A generic curried function for updating a MongoDB collection with a specified filter and update.
 * This function is designed to be flexible and reusable for various operations in different databases
 * and collections.
 *
 * @param filter - A MongoDB query object that determines which documents to update.
 * The filter will be applied to the collection to find matching documents. If no match is found and `upsert` is enabled,
 * - **Filter something**: Specify a condition like `{ name: 'MainSettings' }` to match specific documents.
 * - **Filter nothing**: Use an empty object `{}` to match all documents (or insert a new document if no match is found).
 * a new document will be created.
 * @param options - An optional object that specifies additional options for the update operation. if there is no filter
 * upsert will update the first one it finds if set to true, if false it will not insert anything
 * @example
 * // Example of using the generic `addData` function to add a category:
 * const addCategory = addData('Next_JS_Portfolio')('Settings');
 * await addCategory({ name: 'MainSettings' })({ $addToSet: { category: 'Tech' } })();
 */
const addData =
  (dbName: string) =>
  (collectionName: string) =>
  (filter: object) =>
  (update: object) =>
  (
    options: object = { upsert: true } // with upsert true it will create a new document if no match is found and if we get a match it updates it
  ) =>
  async () => {
    try {
      console.log(`Updating ${collectionName} in ${dbName} with`, update);

      const database = mongoClient.db(dbName);
      const collection = database.collection(collectionName);

      const result = await collection.updateOne(filter, update, options);
      console.log("Update result:", result);

      return result;
    } catch (error) {
      console.error(error);
      return error;
    }
  };

const addData_V0 =
  (dbName: string) =>
  async (collectionName: string) =>
  async (filter: object) =>
  async (update: object) =>
  async (options: object = { upsert: true }) => {
    try {
      console.log(`Updating ${collectionName} in ${dbName} with`, update);

      const database = mongoClient.db(dbName);
      const collection = database.collection(collectionName);

      const result = await collection.updateOne(filter, update, options);
      console.log("Update result:", result);

      return result;
    } catch (error) {
      console.error(error);
      throw new Error("Database update failed");
    }
  };

type addedData = {
  acknowledged: boolean;
  modifiedCount: number;
  upsertedId: null | string;
  upsertedCount: number;
  matchedCount: number;
};

const modifyData =
  (dbName: string) =>
  (collectionName: string) =>
  (filter: object) =>
  (update: object) =>
  (operation: "lookup" | "delete" | "add") =>
  (options: object = { upsert: true }) =>
  async () => {
    // async (operation: "lookup" | "add" = "add") => {
    try {
      console.log(`Performing ${operation} on ${collectionName} in ${dbName}`);

      const database = mongoClient.db(dbName);
      const collection = database.collection(collectionName);

      //returns null if nothing is found and returns the document if it is found
      if (operation === "lookup") {
        const existingData = await collection.findOne(filter);
        console.log("Lookup result:", existingData);
        //return existingData;
        if (existingData)
          return { ...existingData, _id: existingData._id.toString() };
        return existingData;
      }

      if (operation === "delete") {
        const result = await collection.deleteOne(filter);
        console.log("Delete result:", result);
        return result;
      }

      if (operation === "add") {
        console.log("hit promo");
        const result = await collection.updateOne(filter, update, options);
        console.log("Update/Add result:", result);
        return result;
      }

      throw new Error("Invalid operation. Use 'lookup' or 'add'.");
    } catch (error) {
      console.error(error);
      return error;
    }
  };

///original
// export const addNewCategory = (category: string) =>
//   addData('Next_JS_Portfolio')('Settings')({ name: 'MainSettings' })({ $addToSet: { category } })();

//this is the one that was working
// const addUser = async (userEmail: string, userPassword: string) =>
//     addData("Next_JS_Portfolio")("Users")({email: userEmail })({
//     $set: { email: userEmail, password: userPassword },
//   })();

////////////////////////////////////////////////////////////////////////////////////////////////////////////
// const addIpAddress = (userEmail: string, ipAddress: string) =>


  const lookUpCreditsReamaining =  (userID: ObjectId) =>
  modifyData("Next_JS_Portfolio")("Users")({ _id: userID })({})(`lookup`)();


const setTrialResetDate = (userID: ObjectId) =>
  modifyData("Next_JS_Portfolio")("Users")({ _id: userID })({
    $set: {
      lastTrialResetAt: new Date().toISOString(),
      creditsRemaining: 3,
    },
  })("add")();

const setAMTTrialResetDate = (userID: ObjectId) =>
  modifyData("Next_JS_Portfolio")("Users")({ _id: userID })({
    $set: {
      amtlastTrialResetAt: new Date().toISOString(),
    },
  })("add")();

  const setNewCreditAmmount = (userID: ObjectId,creditAmount: number) =>
  modifyData("Next_JS_Portfolio")("Users")({ _id: userID })({
    $set: {
      creditsRemaining: (creditAmount - 1),
    },
  })("add")();

const addFAAFavoriteQuestion = (userID: ObjectId, favoriteQuestion: FAAQuestion) =>
  modifyData("Next_JS_Portfolio")("Users")({ _id: userID })({
    $addToSet: {
      faaFavoriteQuestions: favoriteQuestion,
    },
  })("add")({ upsert: false });

const removeFAAFavoriteQuestion = (userID: ObjectId, favoriteQuestion: FAAQuestion) =>
  modifyData("Next_JS_Portfolio")("Users")({ _id: userID })({
    $pull: {
      faaFavoriteQuestions: { question: favoriteQuestion.question },
    },
  })("add")({ upsert: false });

export async function getFAAFavoriteQuestions(userID: string): Promise<FAAQuestion[] | undefined> {
    const objectId = new ObjectId(userID);
    const user = await modifyData("Next_JS_Portfolio")("Users")({ _id: objectId })({})("lookup")()() as User | null;
    return user?.faaFavoriteQuestions;
}

export async function isCreditsRemaining(userID: string) {
  // this needs to be modified
  const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
  const $24HoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
  console.log("hit promo");

  const lookupUserID = (userID: ObjectId) =>
    modifyData("Next_JS_Portfolio")("Users")({
      _id: userID,
      lastTrialResetAt: {
        $gte: $24HoursAgo.toISOString(),
      },
    })({})("lookup")();
  const objectId = new ObjectId(userID);
  let result = await lookupUserID(objectId)() as User | null;
  console.log(result);
  if (result == null) {
    setTrialResetDate(objectId)();
    return true;
  }
  console.log(result);
  if (result ){
    await setNewCreditAmmount(objectId, result.creditsRemaining)();
    if (result.creditsRemaining > 0) {
      return true;
    }
    return false
  }
}

export async function isAMTCreditsRemaining(userID: string) {
  const $1WeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000); // 7 days ago
  const objectId = new ObjectId(userID);

  const findUser = modifyData("Next_JS_Portfolio")("Users")({ _id: objectId })({})("lookup")();
  const user = await findUser() as User | null;

  if (!user) {
    console.error(`User with ID ${userID} not found.`);
    return false;
  }

  if (!user.amtlastTrialResetAt) {
    await setAMTTrialResetDate(objectId)();
    return true;
  }

  return new Date(user.amtlastTrialResetAt) >= $1WeekAgo;
}
////////////////////////////////////////////////////////////////////
export async function addFAAQuestions(userID: string, question: FAAQuestion, skillLevel?: string) {
  const objectId = new ObjectId(userID);
  const defaultSkillLevel = skillLevel || "somewhat bad";
  const questionWithSkillLevel = { ...question, skillLevel: defaultSkillLevel };

  // First try to update if it exists, otherwise add as new favorite
  let updateResult = await updateFAAQuestionSkillLevel(objectId, question, defaultSkillLevel)();

  // If no document was modified (question doesn't exist), add it as new favorite
  if (updateResult && (updateResult as any).modifiedCount === 0) {
    console.log("Question not found in favorites, adding as new favorite");
    let addResult = await addFAAFavoriteQuestion(objectId, questionWithSkillLevel)();
    console.log("Added new favorite:", addResult);
    return addResult;
  }

  console.log("Updated existing favorite:", updateResult);
  return updateResult;
}

const updateFAAQuestionSkillLevel = (userID: ObjectId, question: FAAQuestion, skillLevel: string) =>
  modifyData("Next_JS_Portfolio")("Users")({
    _id: userID,
    "faaFavoriteQuestions.question": question.question
  })({
    $set: {
      "faaFavoriteQuestions.$.skillLevel": skillLevel
    }
  })("add")({ upsert: false });

export async function updateFAAQuestionSkillLevelPublic(userID: string, question: FAAQuestion, skillLevel: string) {
  const objectId = new ObjectId(userID);

  console.log("Attempting to update skill level for question:", question.question, "to:", skillLevel);

  // First try to update existing question
  let updateResult = await updateFAAQuestionSkillLevel(objectId, question, skillLevel)();

  console.log("Update result:", updateResult);

  // If no document was modified (question doesn't exist), add it as new favorite
  if (updateResult && (updateResult as any).modifiedCount === 0) {
    console.log("Question not found in favorites, adding as new favorite");
    const questionWithSkillLevel = { ...question, skillLevel };
    let addResult = await addFAAFavoriteQuestion(objectId, questionWithSkillLevel)();
    console.log("Added new favorite:", addResult);
    return addResult;
  }

  console.log("Successfully updated skill level:", updateResult);
  return updateResult;
}

export async function removeFAAQuestion(userID: string, question: FAAQuestion) {
  const objectId = new ObjectId(userID);
  console.log("Attempting to remove favorite question:", question.question);
  let faaResult = await removeFAAFavoriteQuestion(objectId, question)();
  console.log("Remove result:", faaResult);
  return faaResult;
}

// O&P Favorites Functions
const addOPFavoriteQuestionDB = (userID: ObjectId, favoriteQuestion: OPQuestion) =>
  modifyData("Next_JS_Portfolio")("Users")({ _id: userID })({
    $addToSet: {
      opFavoriteQuestions: favoriteQuestion,
    },
  })("add")({ upsert: true });

const removeOPFavoriteQuestionDB = (userID: ObjectId, favoriteQuestion: OPQuestion) =>
  modifyData("Next_JS_Portfolio")("Users")({ _id: userID })({
    $pull: {
      opFavoriteQuestions: { ID: favoriteQuestion.ID, ACS_Code: favoriteQuestion.ACS_Code },
    },
  })("add")({ upsert: false });

const updateOPFavoriteQuestionTierDB = (userID: ObjectId, questionID: number, acsCode: string, newTier: 1 | 2 | 3) =>
  modifyData("Next_JS_Portfolio")("Users")({
    _id: userID,
    "opFavoriteQuestions.ID": questionID,
    "opFavoriteQuestions.ACS_Code": acsCode
  })({
    $set: {
      "opFavoriteQuestions.$.favoriteTier": newTier,
    },
  })("add")({ upsert: false });

export async function getOPFavoriteQuestions(userID: string): Promise<OPQuestion[] | undefined> {
    const objectId = new ObjectId(userID);
    const user = await modifyData("Next_JS_Portfolio")("Users")({ _id: objectId })({})("lookup")()() as User | null;
    return user?.opFavoriteQuestions;
}

export async function addOPFavoriteQuestion(userID: string, question: OPQuestion, tier: 1 | 2 | 3 = 1) {
  try {
    // Validate userID format
    if (!userID || typeof userID !== 'string') {
      throw new Error("Invalid userID provided");
    }

    // Check if userID is a valid ObjectId format
    if (!ObjectId.isValid(userID)) {
      throw new Error("Invalid ObjectId format for userID: " + userID);
    }

    const objectId = new ObjectId(userID);
    const questionWithTier = { ...question, favoriteTier: tier };
    console.log("Adding OP favorite - UserID:", userID, "ObjectId:", objectId, "Question:", questionWithTier);

    // First check if user exists
    const user = await modifyData("Next_JS_Portfolio")("Users")({ _id: objectId })({})("lookup")()();
    console.log("User lookup result:", user);

    if (!user) {
      console.error("User not found with ID:", userID);
      throw new Error("User not found");
    }

    let opResult = await addOPFavoriteQuestionDB(objectId, questionWithTier)();
    console.log("Added OP favorite result:", opResult);

    // Check if the operation was successful
    if (opResult && (opResult.acknowledged || opResult.modifiedCount > 0 || opResult.upsertedCount > 0)) {
      console.log("Successfully added OP favorite");
      return opResult;
    } else {
      console.error("Failed to add OP favorite - no changes made");
      throw new Error("Database operation failed");
    }
  } catch (error) {
    console.error("Error in addOPFavoriteQuestion:", error);
    throw error;
  }
}

export async function removeOPFavoriteQuestion(userID: string, question: OPQuestion) {
  const objectId = new ObjectId(userID);
  let opResult = await removeOPFavoriteQuestionDB(objectId, question)();
  console.log("Removed OP favorite:", opResult);
  return opResult;
}

export async function updateOPFavoriteQuestionTier(userID: string, questionID: number, acsCode: string, newTier: 1 | 2 | 3) {
  const objectId = new ObjectId(userID);
  let opResult = await updateOPFavoriteQuestionTierDB(objectId, questionID, acsCode, newTier)();
  console.log("Updated OP favorite tier:", opResult);
  return opResult;
}

// Update notes for an OP question
const updateOPFavoriteQuestionNotesDB = (userID: ObjectId, questionID: number, acsCode: string, notes: string) =>
  modifyData("Next_JS_Portfolio")("Users")({
    _id: userID,
    "opFavoriteQuestions.ID": questionID,
    "opFavoriteQuestions.ACS_Code": acsCode
  })({
    $set: {
      "opFavoriteQuestions.$.notes": notes,
    },
  })("add")({ upsert: false });

export async function updateOPFavoriteQuestionNotes(userID: string, questionID: number, acsCode: string, notes: string) {
  const objectId = new ObjectId(userID);
  console.log("🔍 Attempting to update notes for:", { userID, questionID, acsCode, notesLength: notes.length });

  // Try a different approach - use $addToSet to ensure the field exists, then $set to update it
  // First, let's try the original approach
  let opResult = await updateOPFavoriteQuestionNotesDB(objectId, questionID, acsCode, notes)();
  console.log("🔍 Update operation result:", opResult);

  // Check MongoDB operation result
  if (opResult && typeof opResult === 'object') {
    if ('matchedCount' in opResult) {
      console.log("🔍 MongoDB matchedCount:", opResult.matchedCount);
      console.log("🔍 MongoDB modifiedCount:", opResult.modifiedCount);

      if (opResult.matchedCount === 0) {
        console.log("❌ No documents matched the update query - the question might not exist in favorites or query is wrong");
      } else if (opResult.modifiedCount === 0) {
        console.log("❌ Document found but not modified - trying alternative approach");

        // Try alternative approach: remove and re-add the entire question with notes
        console.log("🔍 Trying alternative update approach...");

        // First get the current question data
        const user = await modifyData("Next_JS_Portfolio")("Users")({ _id: objectId })({})("lookup")()() as any;
        if (user && user.opFavoriteQuestions) {
          const existingQuestion = user.opFavoriteQuestions.find((q: any) =>
            q.ID === questionID && q.ACS_Code === acsCode
          );

          if (existingQuestion) {
            console.log("🔍 Found existing question:", existingQuestion);

            // Update the question with notes
            const updatedQuestion = { ...existingQuestion, notes };

            // Remove the old question and add the updated one
            const removeResult = await modifyData("Next_JS_Portfolio")("Users")({ _id: objectId })({
              $pull: {
                opFavoriteQuestions: { ID: questionID, ACS_Code: acsCode }
              }
            })("add")({ upsert: false })();

            console.log("🔍 Remove result:", removeResult);

            const addResult = await modifyData("Next_JS_Portfolio")("Users")({ _id: objectId })({
              $addToSet: {
                opFavoriteQuestions: updatedQuestion
              }
            })("add")({ upsert: false })();

            console.log("🔍 Add result:", addResult);
            return addResult;
          }
        }
      } else {
        console.log("✅ Update successful");
      }
    }
  }

  return opResult;
}

// Delete notes for an OP question (remove notes field entirely)
const deleteOPFavoriteQuestionNotesDB = (userID: ObjectId, questionID: number, acsCode: string) =>
  modifyData("Next_JS_Portfolio")("Users")({
    _id: userID,
    "opFavoriteQuestions.ID": questionID,
    "opFavoriteQuestions.ACS_Code": acsCode
  })({
    $unset: {
      "opFavoriteQuestions.$.notes": 1,
    },
  })("add")({ upsert: false });

export async function deleteOPFavoriteQuestionNotes(userID: string, questionID: number, acsCode: string) {
  const objectId = new ObjectId(userID);
  console.log("🔍 Attempting to delete notes for:", { userID, questionID, acsCode });
  let opResult = await deleteOPFavoriteQuestionNotesDB(objectId, questionID, acsCode)();
  console.log("🔍 Delete operation result:", opResult);
  return opResult;
}

const addUser = (
  userEmail: string,
  userPassword: string,
  hashedPassword: string
) =>
  modifyData("Next_JS_Portfolio")("Users")({ email: userEmail })({
    $set: {
      email: userEmail,
      password: userPassword,
      hashedPassword: hashedPassword,
    },
  })("add")();

export async function lookupUserHashed(email: string, userPassword: string) {
  try {
    const lookupUserData = (userEmail: string) =>
      modifyData("Next_JS_Portfolio")("Users")({ email: userEmail })({})(
        "lookup"
      )();

    const user = await lookupUserData(email)();

    if (!user) return null;

    const passwordMatch = await compare(userPassword, user.password);
    if (!passwordMatch) return null;

    return { ...user, _id: user._id.toString() };
  } catch (error) {
    console.error("User lookup failed:", error);
    throw new Error("Authentication failed");
  }
}

export async function upsertUser(...args: Parameters<typeof addUser>) {
  try {
    const hashedPassword = await hash(args[1], SALT_ROUNDS);
    args.push(hashedPassword);
    const newuser = await addUser(...args)();
    console.log("User creation result:", newuser);
    return { ...newuser, upsertedId: newuser.upsertedId?.toString() };
  } catch (error) {
    console.error("User creation failed:", error);
    throw new Error("Failed to create user");
  }
}

// ... (keep other existing functions)

const addFingerprint = (userEmail: string, fingerprint: string) =>
  modifyData("Next_JS_Portfolio")("Users")({ email: userEmail })({
    $set: {
      email: userEmail,
      fingerprint: fingerprint,
      fingerprintLastUpdated: new Date().toISOString(),
    },
  })("add")();

export async function lookupRecentFingerprint(fingerprint: string) {
  const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);

  const lookupFingerprint = (fingerprint: string) =>
    modifyData("Next_JS_Portfolio")("Users")({
      fingerprint: fingerprint,
      fingerprintLastUpdated: {
        $gte: fiveMinutesAgo.toISOString(),
      },
    })({})("lookup")();

  let result = await lookupFingerprint(fingerprint)();
  console.log("Recent fingerprint lookup result:", result);
  if (result)
    throw new Error(
      "Your trying to make too many accounts, please try again later"
    );
  return result;
}

// export const addUser = (userEmail: string, userPassword: string) => {
//   console.log("Adding user:", { email: userEmail, password: userPassword });

//   return  addData("Next_JS_Portfolio")("Users")({})({
//     $set: { email: userEmail, password: userPassword },
//   })();
// };

/**
 *checks if a useremail exits. If no email is found it will create a new user
 *
 */
export async function lookupUser(email: string, userPassword: string) {
  console.log(email, userPassword);
  const lookupUser = (userEmail: string, userPassword: string) =>
    modifyData("Next_JS_Portfolio")("Users")({
      email: userEmail,
      password: userPassword,
    })({})(`lookup`)();
  let newuser = await lookupUser(email, userPassword)();
  console.log(newuser);
  // if (newuser) return {...newuser, _id: newuser._id.toString()};
  // else return (newuser)
  return newuser;
}

export async function lookupEmailOnly(email: string) {
  const lookupUser = (userEmail: string) =>
    modifyData("Next_JS_Portfolio")("Users")({ email: userEmail })({})(
      `lookup`
    )();
  let newuser = await lookupUser(email)();
  console.log(newuser);
  // if (newuser) return {...newuser, _id: newuser._id.toString()};
  // else return (newuser)
  return newuser;
}

export async function deleteUser(...args: Parameters<typeof addUser>) {
  let emailArg = args[0];
  if (emailArg.toLowerCase() == "<EMAIL>") {
    return "you cannot delete a test account";
  }
  console.log(emailArg);
  const deleteUser = async (userEmail: string, userPassword: string) =>
    modifyData("Next_JS_Portfolio")("Users")({
      email: userEmail,
      password: userPassword,
    })({})("delete")();
  let newuser = (await deleteUser(...args))();
  return newuser;
}

export async function upsertFingerprint(
  ...args: Parameters<typeof addFingerprint>
) {
  console.log("running");
  let newuser = addFingerprint(...args)();
  let result = (await newuser) as addedData;
  console.log(result);
  if (result.upsertedId)
    return { ...result, upsertedId: result.upsertedId.toString() };
  if (!result.upsertedId) return { ...result };

  //newuser()
  // const addUser = (userEmail: string, userPassword: string) =>
  //await newuser
}

const addIpAddress = (userEmail: string, ipAddress: string) =>
  modifyData("Next_JS_Portfolio")("Users")({ email: userEmail })({
    $set: {
      email: userEmail,
      ipAddress: ipAddress,
      ipLastUpdated: new Date().toISOString(),
    },
  })("add")();

export async function lookupRecentIpAddress(ipAddress: string) {
  const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);

  const lookupIp = (ipAddress: string) =>
    modifyData("Next_JS_Portfolio")("Users")({
      ipAddress: ipAddress,
      ipLastUpdated: {
        $gte: fiveMinutesAgo.toISOString(),
      },
    })({})("lookup")();

  let result = await lookupIp(ipAddress)();
  console.log("Recent IP lookup result:", result);
  if (result)
    throw new Error(
      "Too many accounts from this IP address, please try again later"
    );
  return result;
}

export async function upsertIpAddress(
  ...args: Parameters<typeof addIpAddress>
) {
  console.log("upserting IP address");
  let newuser = addIpAddress(...args)();
  let result = (await newuser) as addedData;
  console.log(result);
  if (result.upsertedId)
    return { ...result, upsertedId: result.upsertedId.toString() };
  if (!result.upsertedId) return { ...result };
}

/**
 * Server action to add a new user
 */
// export const addUser = async (userEmail: string, userPassword: string) => {
//   const addToUsers =  addData("Next_JS_Portfolio");
//   const addToUsersCollection = await addToUsers("Users");
//   const applyFilter = await addToUsersCollection({});
//   const applyUpdate = await applyFilter({
//     $set: { email: userEmail, password: userPassword },
//   });
//   return await applyUpdate(); // Execute the final step
// };
