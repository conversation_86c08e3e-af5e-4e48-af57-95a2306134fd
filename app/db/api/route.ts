"use server";

import { NextResponse } from "next/server";
import { mongoClient } from "@/lib/mongo";
import { ObjectId } from "mongodb";

const addData =
  (dbName: string) =>
  (collectionName: string) =>
  (filter: object) =>
  (update: object) =>
  (options: object = { upsert: true }) =>
  async () => {
    try {
      console.log(`Updating ${collectionName} in ${dbName} with filter:`, filter);
      console.log(`Update operation:`, update);

      // Ensure MongoDB client is connected
      await mongoClient.connect();

      const database = mongoClient.db(dbName);
      const collection = database.collection(collectionName);

      // Check if this is a notes update operation (has positional operator)
      const updateObj = update as any;
      const isNotesUpdate = updateObj.$set && updateObj.$set["opFavoriteQuestions.$.notes"];

      if (isNotesUpdate) {
        console.log("🔍 Detected notes update operation, trying positional operator first...");

        try {
          const result = await collection.updateOne(filter, update, options);
          console.log("✅ Positional operator update successful:", result);
          return result;
        } catch (positionalError: any) {
          console.log("❌ Positional operator failed, trying alternative approach...");
          console.log("Positional error:", positionalError.message);

          // Alternative approach: remove and re-add the question with notes
          const filterObj = filter as any;
          const userId = filterObj._id;
          const questionID = filterObj["opFavoriteQuestions.ID"];
          const acsCode = filterObj["opFavoriteQuestions.ACS_Code"];
          const notes = updateObj.$set["opFavoriteQuestions.$.notes"];

          console.log("🔍 Alternative update params:", { userId, questionID, acsCode, notesLength: notes.length });

          // First get the current question data
          console.log("🔍 Looking up user document with userId:", userId);
          const existingDoc = await collection.findOne({ _id: userId });
          console.log("🔍 Found existing document:", existingDoc ? "YES" : "NO");
          if (existingDoc) {
            console.log("🔍 opFavoriteQuestions array length:", existingDoc.opFavoriteQuestions?.length || 0);
          }

          if (existingDoc && existingDoc.opFavoriteQuestions) {
            const existingQuestion = existingDoc.opFavoriteQuestions.find((q: any) =>
              q.ID === questionID && q.ACS_Code === acsCode
            );

            if (existingQuestion) {
              console.log("🔍 Found existing question for alternative update:", existingQuestion);

              // Update the question with notes
              const updatedQuestion = { ...existingQuestion, notes };

              // Remove the old question
              const removeResult = await collection.updateOne(
                { _id: userId },
                { $pull: { opFavoriteQuestions: { ID: questionID, ACS_Code: acsCode } } }
              );
              console.log("🔍 Remove result:", removeResult);

              // Add the updated question
              const addResult = await collection.updateOne(
                { _id: userId },
                { $addToSet: { opFavoriteQuestions: updatedQuestion } }
              );
              console.log("🔍 Add result:", addResult);

              // Verify the update worked
              const verifyDoc = await collection.findOne({ _id: userId });
              const verifyQuestion = verifyDoc?.opFavoriteQuestions?.find((q: any) =>
                q.ID === questionID && q.ACS_Code === acsCode
              );
              console.log("✅ Final verification - question notes:", verifyQuestion?.notes);

              return addResult;
            } else {
              console.log("❌ Question not found in array for alternative update");
              throw new Error("Question not found in favorites array");
            }
          } else {
            console.log("❌ User document or opFavoriteQuestions array not found");
            throw new Error("User document not found");
          }
        }
      } else {
        // Regular update operation
        const result = await collection.updateOne(filter, update, options);
        console.log("Update result:", result);
        return result;
      }
    } catch (error) {
      console.error(error);
      throw error; // Re-throw to be handled by the API route
    }
  };

// Helper function to convert string IDs to ObjectIds in filter
const convertObjectIds = (obj: any): any => {
  if (obj && typeof obj === 'object') {
    const converted = { ...obj };

    // Convert _id field if it's a string
    if (converted._id && typeof converted._id === 'string' && ObjectId.isValid(converted._id)) {
      converted._id = new ObjectId(converted._id);
    }

    // Recursively convert nested objects
    for (const key in converted) {
      if (converted[key] && typeof converted[key] === 'object') {
        converted[key] = convertObjectIds(converted[key]);
      }
    }

    return converted;
  }
  return obj;
};

// ✅ API Route Handler
export async function POST(req: Request) {
  try {
    console.log('🔍 API route called');

    // Parse the request body
    let requestBody;
    try {
      requestBody = await req.json();
      console.log('🔍 Request body parsed successfully:', requestBody);
    } catch (parseError) {
      console.error('❌ Failed to parse request body:', parseError);
      return NextResponse.json({ error: "Invalid JSON in request body" }, { status: 400 });
    }

    const { dbName, collectionName, filter, update } = requestBody;

    // Validate required fields
    if (!dbName || !collectionName || !filter || !update) {
      console.error('❌ Missing required fields:', { dbName, collectionName, filter: !!filter, update: !!update });
      return NextResponse.json({ error: "Missing required fields: dbName, collectionName, filter, update" }, { status: 400 });
    }

    // Convert string IDs to ObjectIds in the filter
    const convertedFilter = convertObjectIds(filter);

    console.log('Original filter:', filter);
    console.log('Converted filter:', convertedFilter);

    // Execute the function chain
    const result = await addData(dbName)(collectionName)(convertedFilter)(update)()();

    return NextResponse.json({ success: true, result }, { status: 200 });
  } catch (error) {
    console.error('❌ API route error:', error);
    console.error('❌ Error stack:', error instanceof Error ? error.stack : 'No stack trace');
    return NextResponse.json({
      error: "Invalid request",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 400 });
  }
}