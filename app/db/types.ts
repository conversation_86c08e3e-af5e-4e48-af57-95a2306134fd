import { ObjectId } from "mongodb";

export interface FAAQuestion {
  question: string;
  options: string[];
  correct_answer: string;
  fig?: string | string[];
  skillLevel?: string;
}

export interface OPQuestion {
  ID: number;
  Question: string;
  ACS_Code: string;
  Answer: string;
  section?: string; // To track which section it came from
  favoriteTier?: 1 | 2 | 3; // Three tiers for favorites
}

export interface User {
  _id: string | ObjectId;
  email: string;
  password?: string;
  hashedPassword?: string;
  creditsRemaining: number;
  lastTrialResetAt: string;
  amtlastTrialResetAt?: string;
  fingerprint?: string;
  fingerprintLastUpdated?: string;
  ipAddress?: string;
  ipLastUpdated?: string;
  faaFavoriteQuestions?: FAAQuestion[];
  opFavoriteQuestions?: OPQuestion[];
}