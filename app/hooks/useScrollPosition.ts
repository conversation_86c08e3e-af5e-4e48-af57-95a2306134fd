import { useEffect, useRef } from 'react';

/**
 * Custom hook to preserve scroll position during component re-renders
 * Useful when data refreshes but you want to maintain user's scroll position
 */
export function useScrollPosition(dependency?: any) {
  const scrollPositionRef = useRef<number>(0);
  const isRestoringRef = useRef<boolean>(false);

  // Save scroll position before dependency changes
  useEffect(() => {
    const saveScrollPosition = () => {
      scrollPositionRef.current = window.scrollY;
    };

    // Save scroll position when dependency is about to change
    if (dependency !== undefined) {
      saveScrollPosition();
    }

    // Save scroll position on unmount
    return saveScrollPosition;
  }, [dependency]);

  // Restore scroll position after component re-renders
  useEffect(() => {
    if (scrollPositionRef.current > 0 && !isRestoringRef.current) {
      isRestoringRef.current = true;
      
      // Use requestAnimationFrame to ensure DOM is ready
      requestAnimationFrame(() => {
        window.scrollTo({
          top: scrollPositionRef.current,
          behavior: 'instant'
        });
        
        // Reset the flag after a short delay
        setTimeout(() => {
          isRestoringRef.current = false;
        }, 100);
      });
    }
  });

  return {
    saveScrollPosition: () => {
      scrollPositionRef.current = window.scrollY;
    },
    restoreScrollPosition: () => {
      if (scrollPositionRef.current > 0) {
        window.scrollTo({
          top: scrollPositionRef.current,
          behavior: 'smooth'
        });
      }
    },
    getCurrentScrollPosition: () => scrollPositionRef.current
  };
}
