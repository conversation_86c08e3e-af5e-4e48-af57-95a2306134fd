'use client'
import React from 'react';
import ReactMarkdown from "react-markdown";
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import 'katex/dist/katex.min.css';

interface NotesDisplayProps {
  notes: string;
  onEdit: () => void;
  onDelete: () => void;
  isCompact?: boolean;
}

const NotesDisplay: React.FC<NotesDisplayProps> = ({
  notes,
  onEdit,
  onDelete,
  isCompact = false
}) => {
  if (!notes.trim()) return null;

  return (
    <div className={`card w-full bg-base-100 shadow-lg border-l-4 border-info ${isCompact ? 'max-w-2xl' : ''}`}>
      <div className="card-body p-4">
        <div className="flex items-center gap-2 mb-3">
          <span className="text-info text-lg">📝</span>
          <h3 className="text-sm font-semibold text-info">Your Notes</h3>
          <div className="ml-auto flex gap-1">
            <button
              onClick={onEdit}
              className="btn btn-ghost btn-xs hover:btn-info"
              title="Edit notes"
            >
              ✏️ Edit
            </button>
            <button
              onClick={onDelete}
              className="btn btn-ghost btn-xs hover:btn-error"
              title="Delete notes"
            >
              🗑️ Delete
            </button>
          </div>
        </div>
        
        <div className="prose prose-sm max-w-none">
          <ReactMarkdown
            remarkPlugins={[remarkGfm, remarkMath]}
            rehypePlugins={[rehypeKatex]}
            components={{
              // Custom styling for different elements
              h1: ({children}) => <h1 className="text-lg font-bold mb-2">{children}</h1>,
              h2: ({children}) => <h2 className="text-base font-bold mb-2">{children}</h2>,
              h3: ({children}) => <h3 className="text-sm font-bold mb-1">{children}</h3>,
              p: ({children}) => <p className="text-sm mb-2 leading-relaxed">{children}</p>,
              ul: ({children}) => <ul className="text-sm mb-2 ml-4 list-disc">{children}</ul>,
              ol: ({children}) => <ol className="text-sm mb-2 ml-4 list-decimal">{children}</ol>,
              li: ({children}) => <li className="mb-1">{children}</li>,
              code: ({children}) => <code className="bg-base-200 px-1 py-0.5 rounded text-xs">{children}</code>,
              pre: ({children}) => <pre className="bg-base-200 p-2 rounded text-xs overflow-x-auto mb-2">{children}</pre>,
              blockquote: ({children}) => <blockquote className="border-l-4 border-info pl-3 italic text-sm mb-2">{children}</blockquote>,
              strong: ({children}) => <strong className="font-semibold">{children}</strong>,
              em: ({children}) => <em className="italic">{children}</em>,
              a: ({children, href}) => <a href={href} className="text-info hover:text-info-focus underline" target="_blank" rel="noopener noreferrer">{children}</a>,
              table: ({children}) => <table className="table table-xs table-zebra mb-2">{children}</table>,
              th: ({children}) => <th className="text-xs font-semibold">{children}</th>,
              td: ({children}) => <td className="text-xs">{children}</td>,
            }}
          >
            {notes}
          </ReactMarkdown>
        </div>
        
        {/* Quick actions */}
        <div className="flex justify-end mt-2 pt-2 border-t border-base-300">
          <div className="text-xs text-base-content/60">
            Supports Markdown formatting and math formulas
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotesDisplay;
