'use client'
import React, { useState, useEffect } from 'react';
import { OPQuestion } from '@/app/db/types';
import { getOPFavoriteQuestions, removeOPFavoriteQuestion, updateOPFavoriteQuestionTier } from '@/app/db/dbCurry';

interface OPFavoritesProps {
  onBack: () => void;
}

type ViewMode = 'tier-selection' | 'flashcards';
type TierFilter = 'all' | 1 | 2 | 3;

const OPFavorites: React.FC<OPFavoritesProps> = ({ onBack }) => {
  const [viewMode, setViewMode] = useState<ViewMode>('tier-selection');
  const [selectedTier, setSelectedTier] = useState<TierFilter>('all');
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [isFlipped, setIsFlipped] = useState(false);
  const [favoriteQuestions, setFavoriteQuestions] = useState<OPQuestion[]>([]);
  const [filteredQuestions, setFilteredQuestions] = useState<OPQuestion[]>([]);
  const [loading, setLoading] = useState(true);

  // Tier configurations
  const tierConfig = {
    1: { name: 'Priority 1', color: 'badge-error', icon: '🔥', description: 'High Priority - Need to review' },
    2: { name: 'Priority 2', color: 'badge-warning', icon: '⚡', description: 'Medium Priority - Review soon' },
    3: { name: 'Priority 3', color: 'badge-info', icon: '📚', description: 'Low Priority - Review later' }
  };

  useEffect(() => {
    loadFavoriteQuestions();
  }, []);

  useEffect(() => {
    if (selectedTier === 'all') {
      setFilteredQuestions(favoriteQuestions);
    } else {
      setFilteredQuestions(favoriteQuestions.filter(q => q.favoriteTier === selectedTier));
    }
    setCurrentQuestionIndex(0);
    setIsFlipped(false);
  }, [selectedTier, favoriteQuestions]);

  const loadFavoriteQuestions = async () => {
    try {
      const userId = localStorage.getItem('userId');
      if (userId) {
        const favorites = await getOPFavoriteQuestions(userId);
        setFavoriteQuestions(favorites || []);
      }
    } catch (error) {
      console.error('Error loading favorite questions:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleTierSelect = (tier: TierFilter) => {
    setSelectedTier(tier);
    setViewMode('flashcards');
  };

  const handleBackToTierSelection = () => {
    setViewMode('tier-selection');
    setSelectedTier('all');
    setCurrentQuestionIndex(0);
    setIsFlipped(false);
  };

  const handleRemoveFavorite = async (question: OPQuestion) => {
    try {
      const userId = localStorage.getItem('userId');
      if (userId) {
        await removeOPFavoriteQuestion(userId, question);
        await loadFavoriteQuestions(); // Reload the list
      }
    } catch (error) {
      console.error('Error removing favorite:', error);
    }
  };

  const handleChangeTier = async (question: OPQuestion, newTier: 1 | 2 | 3) => {
    try {
      const userId = localStorage.getItem('userId');
      if (userId) {
        await updateOPFavoriteQuestionTier(userId, question.ID, question.ACS_Code, newTier);
        await loadFavoriteQuestions(); // Reload the list
      }
    } catch (error) {
      console.error('Error updating tier:', error);
    }
  };

  const currentQuestion = filteredQuestions[currentQuestionIndex];

  const handleNext = () => {
    setIsFlipped(false);
    setCurrentQuestionIndex((prev) => (prev + 1) % filteredQuestions.length);
  };

  const handlePrevious = () => {
    setIsFlipped(false);
    setCurrentQuestionIndex((prev) => (prev - 1 + filteredQuestions.length) % filteredQuestions.length);
  };

  const handleFlip = () => {
    setIsFlipped(!isFlipped);
  };

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (viewMode !== 'flashcards') return;
      
      switch (event.key) {
        case 'ArrowLeft':
          event.preventDefault();
          handlePrevious();
          break;
        case 'ArrowRight':
          event.preventDefault();
          handleNext();
          break;
        case ' ':
        case 'Enter':
          event.preventDefault();
          handleFlip();
          break;
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [viewMode, currentQuestionIndex, filteredQuestions.length]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="loading loading-spinner loading-lg"></div>
      </div>
    );
  }

  // Show loading only when in flashcard mode but no questions loaded
  if (viewMode === 'flashcards' && !currentQuestion) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="text-6xl mb-4">📚</div>
          <h2 className="text-2xl font-bold mb-2">No favorites in this tier</h2>
          <p className="text-base-content/70 mb-4">
            {selectedTier === 'all' 
              ? "You haven't favorited any O&P questions yet." 
              : `No questions in ${tierConfig[selectedTier as 1 | 2 | 3]?.name}.`}
          </p>
          <button 
            onClick={handleBackToTierSelection}
            className="btn btn-primary"
          >
            Back to Tier Selection
          </button>
        </div>
      </div>
    );
  }

  // Tier Selection View
  if (viewMode === 'tier-selection') {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <button
            onClick={onBack}
            className="btn btn-ghost btn-sm"
          >
            ← Back to O&P Selection
          </button>
          <div className="text-center">
            <h1 className="text-3xl font-bold">O&P Favorites</h1>
            <p className="text-sm opacity-70 mt-2">Choose which tier you'd like to study</p>
          </div>
          <div className="w-24"></div> {/* Spacer for centering */}
        </div>

        {favoriteQuestions.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">⭐</div>
            <h2 className="text-2xl font-bold mb-2">No Favorites Yet</h2>
            <p className="text-base-content/70 mb-4">
              Start favoriting O&P questions to see them here!
            </p>
            <button
              onClick={onBack}
              className="btn btn-primary"
            >
              Go to Flashcards
            </button>
          </div>
        ) : (
          <>
            {/* Tier Cards */}
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
              {/* All Tiers Option */}
              <div
                className="card bg-base-100 shadow-xl hover:shadow-2xl transition-all duration-300 cursor-pointer hover:scale-105"
                onClick={() => handleTierSelect('all')}
              >
                <div className="card-body text-center">
                  <h2 className="card-title justify-center text-lg">
                    🎯 All Favorites
                  </h2>
                  <p className="text-sm opacity-70">
                    Study all {favoriteQuestions.length} favorite questions
                  </p>
                  <div className="badge badge-primary badge-lg mt-2">
                    {favoriteQuestions.length} Questions
                  </div>
                </div>
              </div>

              {/* Individual Tier Cards */}
              {Object.entries(tierConfig).map(([tier, config]) => {
                const tierNumber = parseInt(tier) as 1 | 2 | 3;
                const tierQuestions = favoriteQuestions.filter(q => q.favoriteTier === tierNumber);

                return (
                  <div
                    key={tier}
                    className="card bg-base-100 shadow-xl hover:shadow-2xl transition-all duration-300 cursor-pointer hover:scale-105"
                    onClick={() => handleTierSelect(tierNumber)}
                  >
                    <div className="card-body text-center">
                      <h2 className="card-title justify-center text-lg">
                        {config.icon} {config.name}
                      </h2>
                      <p className="text-sm opacity-70">
                        {config.description}
                      </p>
                      <div className={`badge ${config.color} badge-lg mt-2`}>
                        {tierQuestions.length} Questions
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </>
        )}
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-base-200 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <button
            onClick={handleBackToTierSelection}
            className="btn btn-ghost btn-sm"
          >
            ← Back to Tier Selection
          </button>
          <div className="text-center">
            <h1 className="text-2xl font-bold">O&P Favorites</h1>
            <p className="text-lg font-medium text-primary">
              {selectedTier === 'all' ? 'All Tiers' : tierConfig[selectedTier as 1 | 2 | 3]?.name}
            </p>
            <p className="text-sm opacity-70">
              Question {currentQuestionIndex + 1} of {filteredQuestions.length}
            </p>
          </div>
          <div className="dropdown dropdown-end">
            <div tabIndex={0} role="button" className="btn btn-ghost btn-sm">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
              </svg>
            </div>
            <ul tabIndex={0} className="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
              <li>
                <button onClick={() => handleRemoveFavorite(currentQuestion)} className="text-left text-error">
                  🗑️ Remove from Favorites
                </button>
              </li>
              <li className="menu-title">
                <span>Change Tier</span>
              </li>
              {Object.entries(tierConfig).map(([tier, config]) => {
                const tierNumber = parseInt(tier) as 1 | 2 | 3;
                return (
                  <li key={tier}>
                    <button
                      onClick={() => handleChangeTier(currentQuestion, tierNumber)}
                      className="text-left"
                      disabled={currentQuestion.favoriteTier === tierNumber}
                    >
                      {config.icon} {config.name}
                    </button>
                  </li>
                );
              })}
            </ul>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mb-6">
          <progress
            className="progress progress-primary w-full"
            value={currentQuestionIndex + 1}
            max={filteredQuestions.length}
          ></progress>
        </div>

        {/* Flashcard */}
        <div className="flex justify-center mb-6">
          <div
            className="card w-full max-w-2xl h-96 bg-base-100 shadow-xl cursor-pointer transform transition-transform duration-300 hover:scale-105"
            onClick={handleFlip}
          >
            <div className="card-body flex items-center justify-center text-center p-8">
              {!isFlipped ? (
                <div className="space-y-4">
                  <div className="flex justify-center gap-2 mb-4">
                    <div className="badge badge-primary badge-outline">
                      {currentQuestion.ACS_Code}
                    </div>
                    {currentQuestion.favoriteTier && (
                      <div className={`badge ${tierConfig[currentQuestion.favoriteTier].color} badge-outline`}>
                        {tierConfig[currentQuestion.favoriteTier].icon} Tier {currentQuestion.favoriteTier}
                      </div>
                    )}
                  </div>
                  <h2 className="card-title text-xl leading-relaxed">
                    {currentQuestion.Question}
                  </h2>
                  <p className="text-sm opacity-60 mt-4">
                    Click to reveal answer
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="badge badge-success badge-outline mb-4">
                    Answer
                  </div>
                  <p className="text-lg leading-relaxed">
                    {currentQuestion.Answer}
                  </p>
                  <p className="text-sm opacity-60 mt-4">
                    Click to see question again
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Navigation Controls */}
        <div className="flex justify-center gap-4">
          <button
            onClick={handlePrevious}
            className="btn btn-outline"
            disabled={filteredQuestions.length <= 1}
          >
            ← Previous
          </button>
          <button
            onClick={handleFlip}
            className="btn btn-primary"
          >
            {isFlipped ? 'Show Question' : 'Show Answer'}
          </button>
          <button
            onClick={handleNext}
            className="btn btn-outline"
            disabled={filteredQuestions.length <= 1}
          >
            Next →
          </button>
        </div>

        {/* Question Info */}
        <div className="text-center mt-6 text-sm opacity-70">
          <p>ACS Code: {currentQuestion.ACS_Code}</p>
          {currentQuestion.section && <p>Section: {currentQuestion.section}</p>}
        </div>

        {/* Keyboard Shortcuts Help */}
        <div className="text-center mt-4">
          <div className="collapse collapse-arrow bg-base-100">
            <input type="checkbox" />
            <div className="collapse-title text-sm font-medium opacity-70">
              Keyboard Shortcuts
            </div>
            <div className="collapse-content text-sm opacity-70">
              <div className="grid grid-cols-2 gap-2 text-left">
                <div><kbd className="kbd kbd-xs">←</kbd> Previous</div>
                <div><kbd className="kbd kbd-xs">→</kbd> Next</div>
                <div><kbd className="kbd kbd-xs">Space</kbd> Flip Card</div>
                <div><kbd className="kbd kbd-xs">Enter</kbd> Flip Card</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OPFavorites;
