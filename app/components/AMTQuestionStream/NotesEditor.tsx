'use client'
import React, { useState, useRef } from 'react';
import Simple<PERSON><PERSON> from "react-simplemde-editor";
import ReactMarkdown from "react-markdown";
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import "easymde/dist/easymde.min.css";
import 'katex/dist/katex.min.css';

interface NotesEditorProps {
  initialNotes: string;
  onSave: (notes: string) => void;
  onCancel: () => void;
  questionText: string;
}

const NotesEditor: React.FC<NotesEditorProps> = ({
  initialNotes,
  onSave,
  onCancel,
  questionText
}) => {
  const [notes, setNotes] = useState(initialNotes);
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const simpleMDERef = useRef<any>(null);

  console.log('🔍 NotesEditor rendered with initialNotes:', initialNotes);

  const handleSave = () => {
    alert('NotesEditor handleSave called!'); // Debug alert
    console.log('🔍 NotesEditor handleSave called with notes state:', JSON.stringify(notes));
    console.log('🔍 Original initialNotes were:', JSON.stringify(initialNotes));

    // Get the actual value from SimpleMDE editor to ensure we have the latest content
    const editorValue = simpleMDERef.current?.simpleMDE?.value() || notes;
    console.log('🔍 SimpleMDE editor value:', JSON.stringify(editorValue));

    // Use the editor value if it's different from state
    const finalNotes = editorValue !== notes ? editorValue : notes;
    console.log('🔍 Final notes to save:', JSON.stringify(finalNotes));

    try {
      console.log('🔍 About to call onSave with:', JSON.stringify(finalNotes));
      onSave(finalNotes);
      console.log('🔍 onSave call completed successfully');
    } catch (error) {
      console.error('❌ Error calling onSave:', error);
      alert(`Error calling onSave: ${error}`);
    }
  };

  const simpleMDEOptions = {
    spellChecker: false,
    placeholder: "Add your personal notes, mnemonics, formulas, or additional explanations here...\n\nYou can use Markdown formatting:\n- **bold text**\n- *italic text*\n- `code snippets`\n- Math formulas: $E = mc^2$\n- Lists, links, and more!",
    toolbar: [
      "bold", "italic", "strikethrough", "|",
      "heading-1", "heading-2", "heading-3", "|",
      "code", "quote", "|",
      "unordered-list", "ordered-list", "|",
      "link", "image", "|",
      "table", "|",
      {
        name: "math",
        action: (editor: any) => {
          const cm = editor.codemirror;
          const selection = cm.getSelection();
          cm.replaceSelection(`$${selection}$`);
        },
        className: "fa fa-calculator",
        title: "Insert Math Formula",
      },
      "|",
      "preview", "side-by-side", "fullscreen", "|",
      "guide"
    ],
    status: ["autosave", "lines", "words", "cursor"],
    autosave: {
      enabled: false,
    },
    renderingConfig: {
      singleLineBreaks: false,
      codeSyntaxHighlighting: true,
    },
  };

  return (
    <div className="modal modal-open">
      <div className="modal-box max-w-6xl h-5/6 max-h-none">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-bold text-lg">📝 Edit Notes</h3>
          <div className="flex gap-2">
            <button
              onClick={() => setIsPreviewMode(!isPreviewMode)}
              className={`btn btn-sm ${isPreviewMode ? 'btn-primary' : 'btn-outline'}`}
            >
              {isPreviewMode ? '📝 Edit' : '👁️ Preview'}
            </button>
          </div>
        </div>
        
        {/* Question Context */}
        <div className="mb-4 p-3 bg-base-200 rounded-lg">
          <p className="text-sm font-medium mb-2">Question:</p>
          <p className="text-sm">{questionText}</p>
        </div>

        {/* Editor/Preview Area */}
        <div className="h-96 mb-4">
          {!isPreviewMode ? (
            <SimpleMDE
              ref={simpleMDERef}
              value={notes}
              onChange={setNotes}
              options={simpleMDEOptions}
            />
          ) : (
            <div className="h-full overflow-y-auto border border-base-300 rounded-lg p-4 bg-base-100">
              <div className="prose prose-sm max-w-none">
                <ReactMarkdown
                  remarkPlugins={[remarkGfm, remarkMath]}
                  rehypePlugins={[rehypeKatex]}
                >
                  {notes || '*No notes yet. Switch to edit mode to add some!*'}
                </ReactMarkdown>
              </div>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="modal-action">
          <button
            onClick={onCancel}
            className="btn btn-ghost"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="btn btn-primary"
          >
            Save Notes
          </button>
        </div>
      </div>
    </div>
  );
};

export default NotesEditor;
