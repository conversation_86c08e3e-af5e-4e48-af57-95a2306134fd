'use client'
import React, { useState, useEffect } from 'react';
import { opQuestions } from './O&P Cards/O&PQuestions';
import { OPQuestion } from '@/app/db/types';
import { addOPFavoriteQuestion, removeOPFavoriteQuestion, getOPFavoriteQuestions, deleteOPFavoriteQuestionNotes, updateOPFavoriteQuestionNotes } from '@/app/db/dbCurry';
import NotesEditor from './NotesEditor';
import NotesDisplay from './NotesDisplay';

interface Question {
  ID: number;
  Question: string;
  ACS_Code: string;
  Answer: string;
}

interface OPFlashcardsProps {
  onBack: () => void;
}

type ViewMode = 'topic-selection' | 'flashcards';

const OPFlashcards: React.FC<OPFlashcardsProps> = ({ onBack }) => {
  const [viewMode, setViewMode] = useState<ViewMode>('topic-selection');
  const [selectedTopic, setSelectedTopic] = useState<string>('');
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [isFlipped, setIsFlipped] = useState(false);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [isShuffled, setIsShuffled] = useState(false);
  const [favoriteQuestions, setFavoriteQuestions] = useState<OPQuestion[]>([]);
  const [showFavoriteModal, setShowFavoriteModal] = useState(false);
  const [showNotesEditor, setShowNotesEditor] = useState(false);

  // Get available topics from opQuestions
  const availableTopics = Object.keys(opQuestions);

  // Format topic names for display
  const formatTopicName = (topic: string) => {
    return topic
      .replace(/Section_[A-Z]_/, '')
      .replace(/_/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase());
  };

  const handleTopicSelect = (topic: string) => {
    setSelectedTopic(topic);
    const topicQuestions = opQuestions[topic as keyof typeof opQuestions];
    setQuestions(topicQuestions);
    setCurrentQuestionIndex(0);
    setIsFlipped(false);
    setIsShuffled(false);
    setViewMode('flashcards');
  };

  const handleBackToTopicSelection = () => {
    setViewMode('topic-selection');
    setSelectedTopic('');
    setQuestions([]);
    setCurrentQuestionIndex(0);
    setIsFlipped(false);
    setIsShuffled(false);
  };

  // Load user's favorite questions
  const loadFavoriteQuestions = async () => {
    const userId = localStorage.getItem('userId');

    if (userId) {
      try {
        const favorites = await getOPFavoriteQuestions(userId);
        console.log('🔍 Loaded', favorites?.length || 0, 'favorite questions');

        // Debug: Show questions with notes
        const questionsWithNotes = favorites?.filter(q => q.notes) || [];
        console.log('🔍 Questions with notes:', questionsWithNotes.map(q => ({
          ID: q.ID,
          ACS_Code: q.ACS_Code,
          section: q.section,
          favoriteTier: q.favoriteTier,
          notes: q.notes?.substring(0, 30) + '...',
          fullQuestion: q
        })));

        // Debug: Show ALL favorite questions (even without notes)
        console.log('🔍 ALL favorite questions:', favorites?.map(q => ({
          ID: q.ID,
          ACS_Code: q.ACS_Code,
          section: q.section,
          hasNotes: !!q.notes
        })));

        setFavoriteQuestions(favorites || []);
      } catch (error) {
        console.error('❌ Error loading favorite questions:', error);
      }
    }
  };

  // Check if current question is favorited
  const isQuestionFavorited = (question: Question) => {
    const isFavorited = favoriteQuestions.some(fav => fav.ID === question.ID && fav.ACS_Code === question.ACS_Code);
    console.log('Checking if favorited - Question ID:', question.ID, 'ACS_Code:', question.ACS_Code, 'Is favorited:', isFavorited);
    console.log('Current favorites:', favoriteQuestions);
    return isFavorited;
  };

  // Add question to favorites with tier
  const handleAddToFavorites = async (tier: 1 | 2 | 3) => {
    const userId = localStorage.getItem('userId');
    if (userId && currentQuestion) {
      const opQuestion: OPQuestion = {
        ...currentQuestion,
        section: selectedTopic,
        favoriteTier: tier
      };
      await addOPFavoriteQuestion(userId, opQuestion, tier);
      await loadFavoriteQuestions();
      setShowFavoriteModal(false);
    }
  };

  // Remove question from favorites
  const handleRemoveFromFavorites = async () => {
    try {
      const userId = localStorage.getItem('userId');
      if (userId && currentQuestion) {
        const opQuestion: OPQuestion = {
          ...currentQuestion,
          section: selectedTopic
        };
        await removeOPFavoriteQuestion(userId, opQuestion);
        await loadFavoriteQuestions();
      }
    } catch (error) {
      console.error('Error removing from favorites:', error);
    }
  };

  // Get current question's notes
  const getCurrentQuestionNotes = () => {
    if (!currentQuestion) return '';

    const favoriteQuestion = favoriteQuestions.find(fav =>
      fav.ID === currentQuestion.ID && fav.ACS_Code === currentQuestion.ACS_Code
    );

    const notes = favoriteQuestion?.notes || '';
    console.log('🔍 getCurrentQuestionNotes for ID:', currentQuestion.ID, 'ACS_Code:', currentQuestion.ACS_Code);
    console.log('🔍 Looking for match in favorites:', favoriteQuestions.map(f => `ID:${f.ID} ACS:${f.ACS_Code}`));
    console.log('🔍 Found favorite question:', favoriteQuestion ? 'YES' : 'NO');
    if (favoriteQuestion) {
      console.log('🔍 Matched question details:', {
        ID: favoriteQuestion.ID,
        ACS_Code: favoriteQuestion.ACS_Code,
        section: favoriteQuestion.section,
        favoriteTier: favoriteQuestion.favoriteTier,
        hasNotes: !!favoriteQuestion.notes
      });
    }
    console.log('🔍 Notes content:', notes ? `"${notes.substring(0, 50)}..."` : 'EMPTY');

    return notes;
  };

  // Handle opening notes editor
  const handleOpenNotesEditor = () => {
    console.log('🔍 Opening notes editor for question:', currentQuestion?.ID, 'ACS_Code:', currentQuestion?.ACS_Code);
    console.log('🔍 Current notes:', getCurrentQuestionNotes());
    console.log('🔍 showNotesEditor before:', showNotesEditor);
    setShowNotesEditor(true);
    console.log('🔍 showNotesEditor set to true');
  };

  // Handle saving notes
  const handleSaveNotes = async (notes: string) => {
    // alert('OPFlashcards handleSaveNotes called!'); // Debug alert
    console.log('🔍 handleSaveNotes called with notes:', notes);
    console.log('🔍 selectedTopic:', selectedTopic);

    // Debug: Check current notes vs new notes
    const currentNotes = getCurrentQuestionNotes();
    console.log('🔍 Current notes in state:', JSON.stringify(currentNotes));
    console.log('🔍 New notes to save:', JSON.stringify(notes));
    console.log('🔍 Are they identical?', currentNotes === notes);
    const userId = localStorage.getItem('userId');
    console.log('🔍 userId from localStorage:', userId);
    console.log('🔍 currentQuestion:', currentQuestion);

    // Early exit check
    if (!userId) {
      console.log('❌ No userId found - exiting early');
      alert('No user ID found!');
      return;
    }

    if (!currentQuestion) {
      console.log('❌ No currentQuestion found - exiting early');
      alert('No current question found!');
      return;
    }

    console.log('🔍 Passed initial checks, continuing with save...');

    if (userId && currentQuestion) {
      try {
        console.log('🔍 Starting save process...');
        const isAlreadyFavorited = isQuestionFavorited(currentQuestion);
        console.log('🔍 isQuestionFavorited:', isAlreadyFavorited);

        // If question is not favorited, add it as a favorite first
        if (!isAlreadyFavorited) {
          const opQuestion: OPQuestion = {
            ...currentQuestion,
            section: selectedTopic,
            favoriteTier: 3, // Default to lowest priority when adding for notes
            notes: notes
          };
          console.log('🔍 Adding new favorite with notes:', opQuestion);
          console.log('🔍 Question details - ID:', opQuestion.ID, 'ACS_Code:', opQuestion.ACS_Code, 'Section:', opQuestion.section);
          const result = await addOPFavoriteQuestion(userId, opQuestion, 3);
          console.log('🔍 addOPFavoriteQuestion result:', result);
        } else {
          // Update notes for existing favorite using direct database call
          console.log('🔍 Updating existing favorite notes via direct database call');
          console.log('🔍 Update params - ID:', currentQuestion.ID, 'ACS_Code:', currentQuestion.ACS_Code, 'Notes length:', notes.length);

          // Debug: Show the existing favorite question details
          const existingFav = favoriteQuestions.find(fav =>
            fav.ID === currentQuestion.ID && fav.ACS_Code === currentQuestion.ACS_Code
          );
          console.log('🔍 Existing favorite question in state:', existingFav);

          // Call the database function directly
          const result = await updateOPFavoriteQuestionNotes(userId, currentQuestion.ID, currentQuestion.ACS_Code, notes);
          console.log('🔍 Database update result:', result);
        }

        console.log('🔍 Loading favorite questions...');
        await loadFavoriteQuestions();

        // Check immediately after loadFavoriteQuestions
        const immediateNotes = getCurrentQuestionNotes();
        console.log('🔍 Immediately after loadFavoriteQuestions, notes:', immediateNotes ? `"${immediateNotes}"` : 'NOT FOUND');

        // Add a longer delay to ensure state is updated and check the actual content
        setTimeout(() => {
          const updatedNotes = getCurrentQuestionNotes();
          console.log('🔍 After 500ms delay, notes for current question:', updatedNotes ? `"${updatedNotes}"` : 'NOT FOUND');
          console.log('🔍 favoriteQuestions count:', favoriteQuestions.length);

          // Check if the notes actually changed
          if (updatedNotes === notes) {
            console.log('✅ Notes successfully updated to:', `"${notes}"`);
          } else {
            console.log('❌ Notes update failed. Expected:', `"${notes}"`, 'Got:', updatedNotes ? `"${updatedNotes}"` : 'NOT FOUND');
          }
        }, 500);

        console.log('🔍 Save process completed successfully');

        // Force a component re-render to ensure UI updates
        setFavoriteQuestions([...favoriteQuestions]);

        setShowNotesEditor(false);
      } catch (error) {
        console.error('❌ Error saving notes:', error);
        alert(`Failed to save notes: ${error instanceof Error ? error.message : String(error)}`);
      }
    } else {
      console.log('❌ Missing userId or currentQuestion');
    }
  };

  // Handle canceling notes editor
  const handleCancelNotes = () => {
    setShowNotesEditor(false);
  };

  // Handle deleting notes
  const handleDeleteNotes = async () => {
    const userId = localStorage.getItem('userId');
    if (userId && currentQuestion) {
      // Show confirmation dialog
      const confirmed = window.confirm('Are you sure you want to delete these notes? This action cannot be undone.');

      if (confirmed) {
        try {
          console.log('🔍 Deleting notes for question ID:', currentQuestion.ID, 'ACS_Code:', currentQuestion.ACS_Code);
          console.log('🔍 Notes before delete:', getCurrentQuestionNotes());

          const deleteResult = await deleteOPFavoriteQuestionNotes(userId, currentQuestion.ID, currentQuestion.ACS_Code);
          console.log('🔍 Delete operation result:', deleteResult);

          console.log('🔍 Reloading favorite questions...');
          await loadFavoriteQuestions();

          // Check if notes are actually gone
          setTimeout(() => {
            const notesAfterDelete = getCurrentQuestionNotes();
            console.log('🔍 Notes after delete:', notesAfterDelete ? 'STILL EXISTS' : 'DELETED');
          }, 100);

          console.log('✅ Notes deleted successfully');
        } catch (error) {
          console.error('❌ Error deleting notes:', error);
          alert('Failed to delete notes. Please try again.');
        }
      }
    }
  };

  // Monitor favoriteQuestions changes
  useEffect(() => {
    const questionsWithNotes = favoriteQuestions.filter(q => q.notes);
    if (questionsWithNotes.length > 0) {
      console.log('� Loaded', questionsWithNotes.length, 'questions with notes');
    }
  }, [favoriteQuestions]);

  // Load favorites when component mounts
  useEffect(() => {
    loadFavoriteQuestions();
    // Debug: Check if userId exists
    const userId = localStorage.getItem('userId');
    console.log('Component mounted - userId in localStorage:', userId);

    // Test if we can get user data
    if (userId) {
      console.log('Testing user lookup...');
      getOPFavoriteQuestions(userId).then(result => {
        console.log('User lookup test result:', result);
      }).catch(error => {
        console.error('User lookup test error:', error);
      });
    }
  }, []);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      switch (event.key) {
        case 'ArrowLeft':
          event.preventDefault();
          handlePrevious();
          break;
        case 'ArrowRight':
          event.preventDefault();
          handleNext();
          break;
        case ' ':
        case 'Enter':
          event.preventDefault();
          handleFlip();
          break;
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [currentQuestionIndex, questions.length]);

  const shuffleQuestions = () => {
    const shuffled = [...questions].sort(() => Math.random() - 0.5);
    setQuestions(shuffled);
    setCurrentQuestionIndex(0);
    setIsFlipped(false);
    setIsShuffled(true);
  };

  const resetOrder = () => {
    if (selectedTopic) {
      const topicQuestions = opQuestions[selectedTopic as keyof typeof opQuestions];
      setQuestions(topicQuestions);
    }
    setCurrentQuestionIndex(0);
    setIsFlipped(false);
    setIsShuffled(false);
  };

  const currentQuestion = questions[currentQuestionIndex];

  const handleNext = () => {
    setIsFlipped(false);
    setCurrentQuestionIndex((prev) => (prev + 1) % questions.length);
  };

  const handlePrevious = () => {
    setIsFlipped(false);
    setCurrentQuestionIndex((prev) => (prev - 1 + questions.length) % questions.length);
  };

  const handleFlip = () => {
    setIsFlipped(!isFlipped);
  };

  // Show loading only when in flashcard mode but no questions loaded
  if (viewMode === 'flashcards' && !currentQuestion) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="loading loading-spinner loading-lg"></div>
      </div>
    );
  }

  // Topic Selection View
  if (viewMode === 'topic-selection') {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <button
            onClick={onBack}
            className="btn btn-ghost btn-sm"
          >
            ← Back to O&P Selection
          </button>
          <div className="text-center">
            <h1 className="text-3xl font-bold">Select O&P Topic</h1>
            <p className="text-sm opacity-70 mt-2">Choose which section you'd like to study</p>
          </div>
          <div className="w-24"></div> {/* Spacer for centering */}
        </div>

        {/* Topic Cards */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {/* All Topics Option */}
          <div
            className="card bg-base-100 shadow-xl hover:shadow-2xl transition-all duration-300 cursor-pointer hover:scale-105"
            onClick={() => {
              const allQuestions = Object.values(opQuestions).flat();
              setQuestions(allQuestions);
              setSelectedTopic('All Topics');
              setCurrentQuestionIndex(0);
              setIsFlipped(false);
              setIsShuffled(false);
              setViewMode('flashcards');
            }}
          >
            <div className="card-body text-center">
              <h2 className="card-title justify-center text-lg">
                🎯 All Topics
              </h2>
              <p className="text-sm opacity-70">
                Study all {Object.values(opQuestions).flat().length} questions from all sections
              </p>
              <div className="badge badge-primary badge-lg mt-2">
                Complete Set
              </div>
            </div>
          </div>

          {/* Individual Topic Cards */}
          {availableTopics.map((topic) => {
            const topicQuestions = opQuestions[topic as keyof typeof opQuestions];
            const questionCount = topicQuestions.length;

            return (
              <div
                key={topic}
                className="card bg-base-100 shadow-xl hover:shadow-2xl transition-all duration-300 cursor-pointer hover:scale-105"
                onClick={() => handleTopicSelect(topic)}
              >
                <div className="card-body text-center">
                  <h2 className="card-title justify-center text-lg">
                    📚 {formatTopicName(topic)}
                  </h2>
                  <p className="text-sm opacity-70">
                    {questionCount} questions covering {formatTopicName(topic).toLowerCase()}
                  </p>
                  <div className="badge badge-secondary badge-lg mt-2">
                    {questionCount} Questions
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-base-200 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <button
            onClick={handleBackToTopicSelection}
            className="btn btn-ghost btn-sm"
          >
            ← Back to Topic Selection
          </button>
          <div className="text-center">
            <h1 className="text-2xl font-bold">O&P Flashcards</h1>
            <p className="text-lg font-medium text-primary">
              {selectedTopic === 'All Topics' ? 'All Topics' : formatTopicName(selectedTopic)}
            </p>
            <p className="text-sm opacity-70">
              Question {currentQuestionIndex + 1} of {questions.length}
            </p>
            {isShuffled && (
              <div className="badge badge-info badge-sm mt-1">Shuffled</div>
            )}
          </div>
          <div className="flex items-center gap-2">
            {/* Notes Button */}
            <button
              onClick={handleOpenNotesEditor}
              className={`btn btn-circle btn-sm ${getCurrentQuestionNotes() ? 'btn-info' : 'btn-ghost'}`}
              title={getCurrentQuestionNotes() ? 'Edit notes' : 'Add notes'}
            >
              📝
            </button>

            {/* Favorite Button */}
            <button
              onClick={() => {
                if (isQuestionFavorited(currentQuestion)) {
                  handleRemoveFromFavorites();
                } else {
                  setShowFavoriteModal(true);
                }
              }}
              className={`btn btn-circle btn-sm ${isQuestionFavorited(currentQuestion) ? 'btn-warning' : 'btn-ghost'}`}
              title={isQuestionFavorited(currentQuestion) ? 'Remove from favorites' : 'Add to favorites'}
            >
              {isQuestionFavorited(currentQuestion) ? '⭐' : '☆'}
            </button>

            {/* Menu Dropdown */}
            <div className="dropdown dropdown-end">
              <div tabIndex={0} role="button" className="btn btn-ghost btn-sm">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                </svg>
              </div>
              <ul tabIndex={0} className="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
                <li>
                  <button onClick={shuffleQuestions} className="text-left">
                    🔀 Shuffle Questions
                  </button>
                </li>
                <li>
                  <button onClick={resetOrder} className="text-left">
                    📋 Reset Order
                  </button>
                </li>
                <li>
                  <button onClick={handleOpenNotesEditor} className="text-left">
                    📝 {getCurrentQuestionNotes() ? 'Edit Notes' : 'Add Notes'}
                  </button>
                </li>
                <li>
                  <button
                    onClick={() => {
                      if (isQuestionFavorited(currentQuestion)) {
                        handleRemoveFromFavorites();
                      } else {
                        setShowFavoriteModal(true);
                      }
                    }}
                    className="text-left"
                  >
                    {isQuestionFavorited(currentQuestion) ? '⭐ Remove from Favorites' : '☆ Add to Favorites'}
                  </button>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="w-full bg-base-300 rounded-full h-2 mb-6">
          <div 
            className="bg-primary h-2 rounded-full transition-all duration-300"
            style={{ width: `${((currentQuestionIndex + 1) / questions.length) * 100}%` }}
          ></div>
        </div>

        {/* Flashcard */}
        <div className="flex justify-center mb-6">
          <div 
            className="card w-full max-w-2xl h-96 bg-base-100 shadow-xl cursor-pointer transform transition-transform duration-300 hover:scale-105"
            onClick={handleFlip}
          >
            <div className="card-body flex items-center justify-center text-center p-8">
              {!isFlipped ? (
                <div className="space-y-4">
                  <div className="badge badge-primary badge-outline mb-4">
                    {currentQuestion.ACS_Code}
                  </div>
                  <h2 className="card-title text-xl leading-relaxed">
                    {currentQuestion.Question}
                  </h2>
                  <p className="text-sm opacity-60 mt-4">
                    Click to reveal answer
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="badge badge-success badge-outline mb-4">
                    Answer
                  </div>
                  <p className="text-lg leading-relaxed">
                    {currentQuestion.Answer}
                  </p>
                  <p className="text-sm opacity-60 mt-4">
                    Click to see question again
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Notes Display */}
        {getCurrentQuestionNotes() && (
          <div className="flex justify-center mb-6">
            <NotesDisplay
              notes={getCurrentQuestionNotes()}
              onEdit={handleOpenNotesEditor}
              onDelete={handleDeleteNotes}
              isCompact={true}
            />
          </div>
        )}




        {/* Navigation Controls */}
        <div className="flex justify-center gap-4">
          <button
            onClick={handlePrevious}
            className="btn btn-outline"
            disabled={questions.length <= 1}
          >
            ← Previous
          </button>
          <button
            onClick={handleFlip}
            className="btn btn-primary"
          >
            {isFlipped ? 'Show Question' : 'Show Answer'}
          </button>
          <button
            onClick={handleNext}
            className="btn btn-outline"
            disabled={questions.length <= 1}
          >
            Next →
          </button>
        </div>

        {/* Question Info */}
        <div className="text-center mt-6 text-sm opacity-70">
          <p>ACS Code: {currentQuestion.ACS_Code}</p>
          {getCurrentQuestionNotes() && (
            <div className="flex items-center justify-center gap-1 mt-2">
              <span className="text-info">📝</span>
              <span className="text-info">Notes available</span>
            </div>
          )}
        </div>

        {/* Keyboard Shortcuts Help */}
        <div className="text-center mt-4">
          <div className="collapse collapse-arrow bg-base-100">
            <input type="checkbox" />
            <div className="collapse-title text-sm font-medium opacity-70">
              Keyboard Shortcuts
            </div>
            <div className="collapse-content text-sm opacity-70">
              <div className="grid grid-cols-2 gap-2 text-left">
                <div><kbd className="kbd kbd-xs">←</kbd> Previous</div>
                <div><kbd className="kbd kbd-xs">→</kbd> Next</div>
                <div><kbd className="kbd kbd-xs">Space</kbd> Flip Card</div>
                <div><kbd className="kbd kbd-xs">Enter</kbd> Flip Card</div>
              </div>
            </div>
          </div>
        </div>

        {/* Favorite Tier Selection Modal */}
        {showFavoriteModal && (
          <div className="modal modal-open">
            <div className="modal-box">
              <h3 className="font-bold text-lg mb-4">Add to Favorites</h3>
              <p className="py-2 mb-4">Select priority tier for this question:</p>
              <div className="flex flex-col gap-3">
                <button
                  onClick={() => handleAddToFavorites(1)}
                  className="btn btn-error justify-start"
                >
                  <span className="text-lg mr-2">🔥</span>
                  <div className="text-left">
                    <div className="font-semibold">Priority 1 - High Priority</div>
                    <div className="text-sm opacity-70">Need to review urgently</div>
                  </div>
                </button>
                <button
                  onClick={() => handleAddToFavorites(2)}
                  className="btn btn-warning justify-start"
                >
                  <span className="text-lg mr-2">⚡</span>
                  <div className="text-left">
                    <div className="font-semibold">Priority 2 - Medium Priority</div>
                    <div className="text-sm opacity-70">Review soon</div>
                  </div>
                </button>
                <button
                  onClick={() => handleAddToFavorites(3)}
                  className="btn btn-info justify-start"
                >
                  <span className="text-lg mr-2">📚</span>
                  <div className="text-left">
                    <div className="font-semibold">Priority 3 - Low Priority</div>
                    <div className="text-sm opacity-70">Review later</div>
                  </div>
                </button>
              </div>
              <div className="modal-action">
                <button
                  onClick={() => setShowFavoriteModal(false)}
                  className="btn btn-ghost"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Notes Editor */}
        {showNotesEditor && currentQuestion && (
          <NotesEditor
            initialNotes={getCurrentQuestionNotes()}
            onSave={handleSaveNotes}
            onCancel={handleCancelNotes}
            questionText={currentQuestion.Question}
          />
        )}
      </div>
    </div>
  );
};

export default OPFlashcards;
