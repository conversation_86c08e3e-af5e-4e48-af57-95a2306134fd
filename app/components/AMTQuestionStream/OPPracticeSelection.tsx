'use client'
import React, { useState } from 'react';
import InteractiveOPChat from './InteractiveOPChat';
import OPFlashcards from './OPFlashcards';
import OPFavorites from './OPFavorites';

type PracticeMode = 'selection' | 'oral' | 'text' | 'favorites';

const OPPracticeSelection: React.FC = () => {
  const [practiceMode, setPracticeMode] = useState<PracticeMode>('selection');

  const handleBackToSelection = () => {
    setPracticeMode('selection');
  };

  if (practiceMode === 'oral') {
    return <InteractiveOPChat />;
  }

  if (practiceMode === 'text') {
    return <OPFlashcards onBack={handleBackToSelection} />;
  }

  if (practiceMode === 'favorites') {
    return <OPFavorites onBack={handleBackToSelection} />;
  }

  return (
    <div className="min-h-screen bg-base-200 flex items-center justify-center p-4">
      <div className="max-w-4xl w-full">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-base-content mb-4">
            Oral & Practical Practice
          </h1>
          <p className="text-lg text-base-content/70">
            Choose your practice mode to prepare for your AMT checkride
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Oral Practice Card */}
          <div 
            className="card bg-base-100 shadow-xl cursor-pointer transform transition-all duration-300 hover:scale-105 hover:shadow-2xl"
            onClick={() => setPracticeMode('oral')}
          >
            <div className="card-body text-center p-8">
              <div className="flex justify-center mb-4">
                <div className="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center">
                  <svg 
                    className="w-8 h-8 text-primary" 
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path 
                      strokeLinecap="round" 
                      strokeLinejoin="round" 
                      strokeWidth={2} 
                      d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" 
                    />
                  </svg>
                </div>
              </div>
              <h2 className="card-title text-2xl justify-center mb-4">
                Oral Practice
              </h2>
              <p className="text-base-content/70 mb-6">
                Interactive voice-based practice with an AI examiner. 
                Perfect for simulating the real oral exam experience.
              </p>
              <div className="space-y-2 text-sm text-left">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-success rounded-full"></div>
                  <span>Voice interaction with AI examiner</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-success rounded-full"></div>
                  <span>Real-time feedback on answers</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-success rounded-full"></div>
                  <span>Simulates actual checkride experience</span>
                </div>
              </div>
              <div className="card-actions justify-center mt-6">
                <button className="btn btn-primary btn-wide">
                  Start Oral Practice
                </button>
              </div>
            </div>
          </div>

          {/* Text Practice Card */}
          <div 
            className="card bg-base-100 shadow-xl cursor-pointer transform transition-all duration-300 hover:scale-105 hover:shadow-2xl"
            onClick={() => setPracticeMode('text')}
          >
            <div className="card-body text-center p-8">
              <div className="flex justify-center mb-4">
                <div className="w-16 h-16 bg-secondary/20 rounded-full flex items-center justify-center">
                  <svg 
                    className="w-8 h-8 text-secondary" 
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path 
                      strokeLinecap="round" 
                      strokeLinejoin="round" 
                      strokeWidth={2} 
                      d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" 
                    />
                  </svg>
                </div>
              </div>
              <h2 className="card-title text-2xl justify-center mb-4">
                Text Practice (Flashcards)
              </h2>
              <p className="text-base-content/70 mb-6">
                Study with interactive flashcards covering all O&P questions. 
                Great for memorization and quick review.
              </p>
              <div className="space-y-2 text-sm text-left">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-success rounded-full"></div>
                  <span>Interactive flashcard format</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-success rounded-full"></div>
                  <span>Progress tracking</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-success rounded-full"></div>
                  <span>All O&P questions included</span>
                </div>
              </div>
              <div className="card-actions justify-center mt-6">
                <button className="btn btn-secondary btn-wide">
                  Start Flashcards
                </button>
              </div>
            </div>
          </div>

          {/* Favorites Card */}
          <div
            className="card bg-base-100 shadow-xl cursor-pointer transform transition-all duration-300 hover:scale-105 hover:shadow-2xl"
            onClick={() => setPracticeMode('favorites')}
          >
            <div className="card-body text-center p-8">
              <div className="flex justify-center mb-4">
                <div className="w-16 h-16 bg-warning/20 rounded-full flex items-center justify-center">
                  <svg
                    className="w-8 h-8 text-warning"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
                    />
                  </svg>
                </div>
              </div>
              <h2 className="card-title text-2xl justify-center mb-4">
                Favorites
              </h2>
              <p className="text-base-content/70 mb-6">
                Review your favorited O&P questions organized by priority tiers.
                Focus on the questions you need to study most.
              </p>
              <div className="space-y-2 text-sm text-left">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-success rounded-full"></div>
                  <span>Three priority tiers</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-success rounded-full"></div>
                  <span>Personalized study lists</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-success rounded-full"></div>
                  <span>Focus on problem areas</span>
                </div>
              </div>
              <div className="card-actions justify-center mt-6">
                <button className="btn btn-warning btn-wide">
                  View Favorites
                </button>
              </div>
            </div>
          </div>
        </div>

        <div className="text-center mt-8">
          <div className="alert alert-info">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" className="stroke-current shrink-0 w-6 h-6">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span>Both practice modes are designed to help you prepare for your AMT checkride. Choose the one that best fits your learning style!</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OPPracticeSelection;
