'use client'
import { Backbutton } from './backbutton';
import React from 'react';
import Image from 'next/image'; // Import the Image component
import { useState, useEffect } from 'react';
import AMTAppSectionSelection from '@/app/components/AMTQuestionStream/AMTAppSectionSelection'
import { mechanicTestsData, navItemsData } from '@/app/components/AMTQuestionStream/sectionData';
import QuestionComponent from '@/app/components/AMTQuestionStream/QuestionComponent';
import Container from '@mui/material/Container';
import type { corrosion } from '@/app/components/AMTQuestionStream/Question';
import useAdvancedSelect from '@/app/(main site)/Components/ui/AdvancedSelect'
import Link from "next/link";
import { useSearchParams } from 'next/navigation';
import { useRouter } from 'next/router';
import { getFAAFavoriteQuestions } from '@/app/db/dbCurry';
import { digitalServiceDetector } from "@/app/utils/digitalServiceDetector"
import { useScrollPosition } from "@/app/hooks/useScrollPosition"

// Import skill level constants for consistency
const SKILL_LEVEL_OPTIONS = ["really bad", "bad", "somewhat bad"];
const DEFAULT_SKILL_LEVEL = "somewhat bad";

import useBasicToggle from '@/app/(main site)/Components/ui/BasicToggle2';

//these bottom imports are for payments
import useModal from "@/app/components/ui/Modal4";
import { useCallbackModal } from "@/app/components/ui/Modal4.CallBackModal";
import useDevModeToggle from "@/app/components/ai-transcriptions/internalComponents/DevModeToggle"
import { isUserSubscribed } from "@/app/utils/isUserSubscribed"
//end imports associated with payments logic


function App() {

    const searchParams = useSearchParams();
    const displayParam = searchParams.get('display')
    console.log(displayParam)
    const [isDevModeToggled, , DevModeToggle] = useBasicToggle({ leftText: 'Dev Mode', RightText: 'Normal Mode' })
    const [currentQuestionSet, setCurrentQuestionSet] = useState<any[] | null>(null);
    const [isOnHomeScreen, setIsOnHomeScreen] = useState(true);
    const [questions, setQuestions] = useState<typeof corrosion | null>(null);
    const [selectedTestTitle, setSelectedTestTitle] = useState<string | null>(null);
    const [questionBanks, setQuestionBanks] = useState([])
    const [isLoadingQuestions, setIsLoadingQuestions] = useState(false);
    const { selectedOption: AISelectOutput, setSelectedOption, BasicSelect: AISelect } = useAdvancedSelect({ options: questionBanks, maintext: 'Select Your QUestion Bank' })
    const [userId, resetUserId] = useState(() => {
        if (typeof window !== 'undefined' && window.localStorage) {
            console.log(localStorage)
            return localStorage.getItem('userId');// a mod to lazy initialize the userid instead of using clerk
        }
        return null;
    });
    /// all of this is for payments
    const [GenericModal, genericRef] = useModal();
    const [genericModalText, setGenericModalText] = useState('')
    const [toggled, DevToggleButton] = useDevModeToggle();
    const [isQuestionPreview, setIsQuestionPreview] = useState(false);
    const [GenericCallBackModal, showCallbackModal, closeCallbackModal, setModalCallback] = useCallbackModal();
    // Skill level filter state for favorites
    const [skillLevelFilter, setSkillLevelFilter] = useState<string>('All');
    const [isViewingFavorites, setIsViewingFavorites] = useState<boolean>(false);

    // Function to handle skill level filter changes
    const handleSkillLevelFilterChange = (newFilter: string) => {
        console.log('Changing skill level filter to:', newFilter);
        // Save scroll position before filtering
        saveScrollPosition();
        setSkillLevelFilter(newFilter);
        // Re-fetch favorites with the new filter
        const favoritesSection = mechanicTestsData.find(section => section.questions === 'FAVORITES');
        if (favoritesSection) {
            handleImageClick(favoritesSection, newFilter);
        }
        // Restore scroll position after a short delay
        setTimeout(() => {
            restoreScrollPosition();
        }, 100);
    };

    // Function to handle skill level changes without refreshing
    const handleSkillLevelChange = () => {
        console.log('Skill level changed - no refresh needed, database updated by QuestionComponent');
        // Do nothing - the QuestionComponent already handles the database update
        // No need to refresh the entire favorites list
    };


    const genericModal = (
    <GenericModal>
      <div className="p-6 prose bg-gray-900 rounded-lg text-white">
        <h2 className="text-2xl font-bold text-blue-400 mb-4">{genericModalText}</h2>
        <div className="flex justify-end gap-3 mt-6">
          <button className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors shadow-md" onClick={() => genericRef.current?.close()}>
            Close
          </button>
        </div>
      </div>
    </GenericModal>
    ); 
   //end stuff for payments
    const [questionFontSize, setQuestionFontSize] = useState(16); // Add this state
    const [numberOfQuestions, setNumberOfQuestions] = useState(100);
    const [totalQuestionsAvailable, setTotalQuestionsAvailable] = useState(0);



    console.log(userId)
    console.log(AISelectOutput)
    console.log(questionBanks)
    console.log(questionBanks.length >0 )
 

    console.log("questions set to", questions)
    const backButtonHandler = () => {
        setIsOnHomeScreen(true);
        setIsViewingFavorites(false);
        setSkillLevelFilter('All');
        const url = new URL(window.location);
            url.searchParams.delete('display');
            window.history.pushState({}, '', url);
    };

    const [isLoadingUser, setIsLoadingUser] = useState(true);

    // Add scroll position preservation as backup
    const { saveScrollPosition, restoreScrollPosition } = useScrollPosition(questions);

    useEffect(() => {
        console.log("questions set to", questions)

        if (currentQuestionSet != null && AISelectOutput) {
            console.log('we got changed to', AISelectOutput)
            console.log('currentQuestionSet is', currentQuestionSet)
            const practicalQuestionObj = currentQuestionSet.find(q => AISelectOutput in q);
            const practicalQuestion = practicalQuestionObj?.[AISelectOutput]
            setQuestions(practicalQuestion)
        }
        setIsLoadingUser(false);
        digitalServiceDetector()
    }, [AISelectOutput, currentQuestionSet]);

    useEffect(() => {
        const checkSubscription = async () => {
            const subscribed = await isUserSubscribed(userId, setGenericModalText, genericRef, toggled, showCallbackModal, () => { setIsOnHomeScreen(false) }, () => {
                setIsOnHomeScreen(false)
                setIsQuestionPreview(true)});
           // setIsSubscribed(subscribed);
        };
        if (userId) checkSubscription();
    }, [userId, toggled]);


    const handleImageClick = async (sectionQuestions: typeof mechanicTestsData[6], overrideSkillFilter?: string) => {
        if (sectionQuestions.questions === 'FAVORITES') {
            setIsViewingFavorites(true);
            if (userId) {
                setIsLoadingQuestions(true);
                const favoriteQuestions = await getFAAFavoriteQuestions(userId);
                if (favoriteQuestions && favoriteQuestions.length > 0) {
                    // Ensure all questions have skill levels set and normalize them
                    const questionsWithSkillLevels = favoriteQuestions.map(q => {
                        let skillLevel = q.skillLevel || DEFAULT_SKILL_LEVEL;

                        // Normalize skill level - trim whitespace and ensure it's a valid option
                        skillLevel = skillLevel.trim();
                        if (!SKILL_LEVEL_OPTIONS.includes(skillLevel)) {
                            console.warn(`Invalid skill level "${skillLevel}" found, defaulting to "${DEFAULT_SKILL_LEVEL}"`);
                            skillLevel = DEFAULT_SKILL_LEVEL;
                        }

                        return {
                            ...q,
                            skillLevel
                        };
                    });
                    // If no override filter is provided and this is a fresh click on favorites, reset to 'All'
                    let currentFilter = overrideSkillFilter;
                    if (overrideSkillFilter === undefined) {
                        // This is a fresh click on favorites, reset filter to 'All'
                        currentFilter = 'All';
                        setSkillLevelFilter('All');
                    }

                    // Apply skill level filter with exact matching
                    let filteredQuestions = questionsWithSkillLevels;
                    if (currentFilter && currentFilter !== 'All') {
                        console.log('=== FILTERING DEBUG ===');
                        console.log('Filtering favorites with skill level:', `"${currentFilter}"`);
                        console.log('Total favorite questions:', questionsWithSkillLevels.length);

                        // Log all skill levels to see what we have
                        const skillLevelCounts: Record<string, number> = {};
                        questionsWithSkillLevels.forEach(q => {
                            const skillLevel = q.skillLevel || 'undefined';
                            skillLevelCounts[skillLevel] = (skillLevelCounts[skillLevel] || 0) + 1;
                        });
                        console.log('Skill level distribution:', skillLevelCounts);

                        filteredQuestions = questionsWithSkillLevels.filter(q => {
                            const questionSkillLevel = q.skillLevel || DEFAULT_SKILL_LEVEL;
                            const matches = questionSkillLevel === currentFilter;
                            console.log(`Question: "${q.question.substring(0, 30)}..." | Skill: "${questionSkillLevel}" (length: ${questionSkillLevel.length}) | Filter: "${currentFilter}" (length: ${currentFilter.length}) | Match: ${matches}`);

                            // Additional debugging for character-by-character comparison
                            if (questionSkillLevel.includes('bad') && currentFilter.includes('bad')) {
                                console.log(`  Character comparison:`);
                                console.log(`  Question skill: [${questionSkillLevel.split('').map(c => c.charCodeAt(0)).join(', ')}]`);
                                console.log(`  Filter:         [${currentFilter.split('').map(c => c.charCodeAt(0)).join(', ')}]`);
                            }

                            return matches;
                        });
                        console.log('Filtered questions count:', filteredQuestions.length);
                        console.log('=== END FILTERING DEBUG ===');
                    }

                    if (filteredQuestions.length > 0) {
                        const title = currentFilter && currentFilter !== 'All'
                            ? `Your Favorite Questions - ${currentFilter}`
                            : 'Your Favorite Questions';
                        setQuestions([title, filteredQuestions]);
                        setTotalQuestionsAvailable(filteredQuestions.length);
                    } else {
                        setTotalQuestionsAvailable(0);
                        const message = currentFilter && currentFilter !== 'All'
                            ? `You have no favorite questions with skill level: ${currentFilter}`
                            : 'You have no favorite questions yet.';
                        setQuestions([message, []]);
                    }
                } else {
                    setTotalQuestionsAvailable(0);
                    setQuestions(['You have no favorite questions yet.', []]);
                }
                setCurrentQuestionSet(null);
                setQuestionBanks([]);
                setSelectedTestTitle(null); // Reset the test title
                setIsLoadingQuestions(false);
            } else {
                // TODO: Handle user not logged in. Maybe show login modal.
                console.log("User not logged in");
            }
            return;
        } else {
            // When navigating to non-favorites sections, reset the skill level filter
            setIsViewingFavorites(false);
            setSkillLevelFilter('All');
        }

        setSelectedTestTitle(sectionQuestions.title); // Set the title here

        if (sectionQuestions.questions) {
            setIsLoadingQuestions(true);
            await new Promise(resolve => setTimeout(resolve, 300)); // Simulate loading

            if (typeof sectionQuestions.questions === 'function') {
                const allQuestions = sectionQuestions.questions();
                setTotalQuestionsAvailable(allQuestions[1].length);
                const questionsToDisplay = sectionQuestions.questions(numberOfQuestions);
                setQuestions(questionsToDisplay);
                setCurrentQuestionSet(null); // No nested banks for this type
                setQuestionBanks([]);
            } else if (Array.isArray(sectionQuestions.questions) && sectionQuestions.questions.length > 0 && typeof sectionQuestions.questions[0] === 'object' && sectionQuestions.questions[0] !== null) {
                setCurrentQuestionSet(sectionQuestions.questions);
                const questionBanks = sectionQuestions.questions.map(obj => Object.keys(obj)[0])
                const firstBankKey = questionBanks[0];
                const firstBank = sectionQuestions.questions.find(q => firstBankKey in q);
                setTotalQuestionsAvailable(firstBank[firstBankKey][1].length);
                setQuestionBanks(questionBanks);
                setSelectedOption(firstBankKey);
            }
            else {
                setTotalQuestionsAvailable(sectionQuestions.questions[1].length);
                setQuestions(sectionQuestions.questions);
                setCurrentQuestionSet(null);
                setQuestionBanks([])
            }
            setIsLoadingQuestions(false);
        }
    };

    const generateNewPowerplantTest = () => {
        const powerplantTest = mechanicTestsData.find((item): item is MechanicTestItem => item.title === 'PowerPlant' && item.type === 'testItem');
        if (powerplantTest && typeof powerplantTest.questions === 'function') {
            setIsLoadingQuestions(true);
            const allQuestions = powerplantTest.questions();
            setTotalQuestionsAvailable(allQuestions[1].length);
            const newQuestions = powerplantTest.questions(numberOfQuestions);
            setQuestions(newQuestions);
            setIsLoadingQuestions(false);
        }
    };
    // Define your image source.
    // For external images, it's best to define them here or pass as props.
    const backgroundImageSrc = "/amt_app_images/amthome.png";

    if (isLoadingQuestions) {
        return (
            <div className="flex justify-center items-center min-h-screen bg-slate-50">
                <div className="animate-spin rounded-full h-32 w-32 border-t-2 border-b-2 border-blue-500"></div>
            </div>
        );
    }

    if (!isOnHomeScreen && questions) return (
        <Container maxWidth="xl"  >
            <div className="flex flex-col gap-4 overflow-x-hidden relative items-center">
                {/* <div>
                            <AIChatBox titleName="MalcMind - AI AMT APP"
                                AI_product_name="MalcMind AI"
                                hide_settings_button={true}
                            /> 
                        </div> */}
                <Backbutton setQuestions={setQuestions} />
                <div className="bg-white">
                    {questionBanks.length >0 && <AISelect />}
                    {/* Skill Level Filter for Favorites */}
                    {isViewingFavorites && (
                        <div className="flex justify-center items-center p-4 bg-base-200 rounded-lg mb-4">
                            <label className="mr-3 font-medium text-base-content">Filter by Skill Level:</label>
                            <select
                                className="select select-bordered select-sm max-w-xs"
                                value={skillLevelFilter}
                                onChange={(e) => handleSkillLevelFilterChange(e.target.value)}
                            >
                                <option value="All">All Skill Levels</option>
                                {SKILL_LEVEL_OPTIONS.map(option => (
                                    <option key={option} value={option}>{option}</option>
                                ))}
                            </select>
                        </div>
                    )}
                    <QuestionComponent
                        ai_result={questions}
                        questionFontSize={questionFontSize}
                        totalQuestionsAvailable={totalQuestionsAvailable}
                        setQuestionFontSize={setQuestionFontSize}
                        numberOfQuestions={numberOfQuestions}
                        setNumberOfQuestions={setNumberOfQuestions}
                        isQuestionPreview={isQuestionPreview}
                        goHome={backButtonHandler}
                        onGenerateNewTest={selectedTestTitle === 'PowerPlant' ? generateNewPowerplantTest : undefined}
                        onSkillLevelChange={handleSkillLevelChange}
                    />
                </div>
                <Backbutton setQuestions={setQuestions} />

            </div>
        </Container>
    );
    if (!isOnHomeScreen || displayParam == 'delete-account') return <AMTAppSectionSelection
        mechanicTestsData={mechanicTestsData} navItemsData={navItemsData}
        onClick={handleImageClick}
        backButtonHandler={backButtonHandler}
        onHomeClick={backButtonHandler}
        defaultLocation = {displayParam || 'Tests'}
        questionFontSize={questionFontSize}
        setQuestionFontSize={setQuestionFontSize}
    />
    if (isOnHomeScreen) return (
        <div
            className="relative flex size-full min-h-screen max-w-[650px] flex-col bg-slate-50 justify-between group/design-root overflow-x-hidden mx-auto"
            style={{ fontFamily: '"Spline Sans", "Noto Sans", sans-serif' }}
        >
            <div>
                <div className="@container">
                    <div className="@[480px]:px-4 @[480px]:py-3">
                        {/* The main container for the image, now with position: relative */}
                        <div
                            className="relative w-full flex flex-col justify-end overflow-hidden bg-slate-50 @[480px]:rounded-xl min-h-80"
                        // Removed style={{ backgroundImage: 'url(...)' }}
                        >
                            {/* Using next/image component */}
                            <Image
                                src={backgroundImageSrc}
                                alt="Background for FAA Mechanic Exams"
                                width={800} // Add explicit width
                                height={320} // Add explicit height  
                                style={{ objectFit: 'cover' }}
                                className="w-full h-full rounded-xl" // Make it fill container with Tailwind
                            />
                            {/* A div to layer content *over* the image if needed, for example,
                  if you had text or a gradient on top of the background image.
                  It needs a higher z-index than the image.
              */}
                            <div className="absolute inset-0 z-10 flex flex-col justify-end">
                                {/* Any content that should appear over the image goes here */}
                            </div>
                        </div>
                    </div>
                </div>
                <h2 className="text-[#0d141c] tracking-light text-[28px] font-bold leading-tight px-4 text-center pb-3 pt-5">
                    Ace Your A&P Exams
                </h2>
                <p className="text-[#0d141c] text-base font-normal leading-normal pb-3 pt-1 px-4 text-center">
                    Prepare for your FAA Airframe and Powerplant exams with our comprehensive study tools and practice tests.
                </p>
                {isLoadingUser && (
                    <div className="flex px-4 py-3 justify-center">
                        <button
                            disabled
                            className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-12 px-5 bg-gray-400 text-slate-50 text-base font-bold leading-normal tracking-[0.015em]"
                        >
                            <span className="truncate">Loading...</span>
                        </button>
                    </div>
                )}
                {!isLoadingUser && !userId && <Link href="/malcmind-login?app=AMTapp" className="w-full">
                    <div className="flex px-4 py-3 justify-center">
                        <button
                            className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-12 px-5 bg-[#0c7ff2] text-slate-50 text-base font-bold leading-normal tracking-[0.015em]"
                        >
                            <span className="truncate">Get Started</span>
                        </button>
                    </div>
                </Link>}
                {!isLoadingUser && userId &&
                    <div className="flex flex-col items-center px-4 py-3 justify-center gap-2">
                        {userId === '690d75369547d4dbacd72cfe' && <DevModeToggle />}
                        {genericModal}
                        {GenericCallBackModal}
                        {userId == "690d75369547d4dbacd72cfe" && <DevToggleButton />}
                        <button
                            onClick={async () => {
                                console.log('clicked') 
                                let doesUserHaveLogin = await isUserSubscribed(userId, setGenericModalText, genericRef, toggled, showCallbackModal, () => { setIsOnHomeScreen(false) }, () => {
                                    setIsOnHomeScreen(false)
                                    setIsQuestionPreview(true)
                                }) 
                                //setIsOnHomeScreen(false)
                            }}
                            className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-12 px-5 bg-[#0c7ff2] text-slate-50 text-base font-bold leading-normal tracking-[0.015em]"
                        >
                            <span className="truncate">Continue</span>
                        </button>
                        <button
                            onClick={() => {
                                resetUserId('');
                                localStorage.clear();
                                setQuestions(null);
                                setQuestionBanks([]);
                            }}
                            className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-12 px-5 bg-red-500 text-slate-50 text-base font-bold leading-normal tracking-[0.015em]"
                        >
                            <span className="truncate">Logout</span>
                        </button>
                    </div>
                }
            </div>
            {!isLoadingUser && !userId && <Link href="/malcmind-login?app=AMTapp" className="w-full">
                <div>
                    <p className="text-[#49739c] text-sm font-normal leading-normal pb-3 pt-1 px-4 text-center underline">
                        Already have an account? Sign In
                    </p>
                    <div className="h-5 bg-slate-50"></div>
                </div>
            </Link>}

        </div>
    );
}

export default App;
